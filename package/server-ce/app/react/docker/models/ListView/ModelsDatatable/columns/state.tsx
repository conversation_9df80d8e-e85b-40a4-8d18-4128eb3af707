import clsx from 'clsx';
import { CellContext } from '@tanstack/react-table';

import {
  type ModelListViewModel,
  ModelStatus,
} from '@/react/docker/models/types';

import { filterHOC } from '@@/datatables/Filter';
import { multiple } from '@@/datatables/filter-types';

import { columnHelper } from './helper';

export const state = columnHelper.accessor('Status', {
  header: 'State',
  id: 'state',
  cell: StatusCell,
  enableColumnFilter: true,
  filterFn: multiple,
  meta: {
    filter: filterHOC('Filter by state'),
  },
});

function StatusCell({
  getValue,
  row: { original: model },
}: CellContext<ModelListViewModel, ModelStatus>) {
  const status = getValue();

  const statusClassName = getClassName();

  return (
    <span
      className={clsx('label', `label-${statusClassName}`)}
      title={`Model status: ${status}`}
    >
      {status}
    </span>
  );

  function getClassName() {
    switch (status) {
      case ModelStatus.Running:
        return 'success';
      case ModelStatus.Stopped:
      default:
        return 'danger';
    }
  }
}
