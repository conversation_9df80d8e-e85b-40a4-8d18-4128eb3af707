import { CellContext } from '@tanstack/react-table';

import type { ModelListViewModel } from '@/react/docker/models/types';

import { columnHelper } from './helper';

export const size = columnHelper.accessor('Size', {
  header: 'Size',
  id: 'size',
  cell: SizeCell,
});

function SizeCell({ getValue }: CellContext<ModelListViewModel, string>) {
  const modelSize = getValue();

  return (
    <span className="font-mono text-sm">
      {modelSize}
    </span>
  );
}
