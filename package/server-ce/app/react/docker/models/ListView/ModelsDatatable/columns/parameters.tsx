import { CellContext } from '@tanstack/react-table';

import type { ModelListViewModel } from '@/react/docker/models/types';

import { columnHelper } from './helper';

export const parameters = columnHelper.accessor('Parameters', {
  header: 'Parameters',
  id: 'parameters',
  cell: ParametersCell,
});

function ParametersCell({ getValue }: CellContext<ModelListViewModel, string>) {
  const params = getValue();

  return (
    <span className="font-mono text-sm">
      {params}
    </span>
  );
}
