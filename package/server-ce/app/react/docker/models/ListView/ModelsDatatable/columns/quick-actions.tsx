import { CellContext } from '@tanstack/react-table';
import { Eye, FileText, BarChart3, Terminal } from 'lucide-react';

import { useAuthorizations } from '@/react/hooks/useUser';
import { ModelListViewModel } from '@/react/docker/models/types';

import { Button } from '@@/buttons';

import { columnHelper } from './helper';

export const quickActions = columnHelper.display({
  header: 'Quick Actions',
  id: 'actions',
  cell: QuickActionsCell,
});

function QuickActionsCell({
  row: { original: model },
}: CellContext<ModelListViewModel, unknown>) {
  const { authorized } = useAuthorizations([
    'DockerContainerStats',
    'DockerContainerLogs',
    'DockerExecStart',
    'DockerContainerInspect',
  ]);

  if (!authorized) {
    return null;
  }

  return (
    <div className="flex gap-1">
      <Button
        size="xsmall"
        color="light"
        icon={Eye}
        title="Inspect model"
        onClick={() => {
          // TODO: Implement inspect functionality
          console.log('Inspect model:', model.Id);
        }}
      />
      <Button
        size="xsmall"
        color="light"
        icon={FileText}
        title="View logs"
        onClick={() => {
          // TODO: Implement logs functionality
          console.log('View logs:', model.Id);
        }}
      />
      <Button
        size="xsmall"
        color="light"
        icon={BarChart3}
        title="View stats"
        onClick={() => {
          // TODO: Implement stats functionality
          console.log('View stats:', model.Id);
        }}
      />
      <Button
        size="xsmall"
        color="light"
        icon={Terminal}
        title="Console"
        onClick={() => {
          // TODO: Implement console functionality
          console.log('Open console:', model.Id);
        }}
      />
    </div>
  );
}
