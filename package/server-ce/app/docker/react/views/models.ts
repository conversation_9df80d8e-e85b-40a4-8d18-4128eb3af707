import angular from 'angular';
import { StateRegistry } from '@uirouter/angularjs';

import { r2a } from '@/react-tools/react2angular';
import { withCurrentUser } from '@/react-tools/withCurrentUser';
import { withReactQuery } from '@/react-tools/withReactQuery';
import { withUIRouter } from '@/react-tools/withUIRouter';

import { ListView } from '@/react/docker/models/ListView/ListView';

export const modelsModule = angular
  .module('portainer.docker.react.views.models', [])
  .component(
    'modelsView',
    r2a(withUIRouter(withReactQuery(withCurrentUser(ListView))), ['endpoint'])
  )
  .config(config).name;

/* @ngInject */
function config($stateRegistryProvider: StateRegistry) {
  $stateRegistryProvider.register({
    name: 'docker.models',
    url: '/models',
    views: {
      'content@': {
        component: 'modelsView',
      },
    },
    data: {
      docs: '/user/docker/models',
    },
  });
}
