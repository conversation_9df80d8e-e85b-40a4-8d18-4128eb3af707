import { useQuery } from '@tanstack/react-query';

import { EnvironmentId } from '@/react/portainer/environments/types';
import { withGlobalError } from '@/react-tools/react-query';

import { ModelListViewModel, ModelStatus } from '../types';

import { queryKeys } from './query-keys';

interface UseModels {
  all?: boolean;
  nodeName?: string;
}

export function useModels<T = ModelListViewModel[]>(
  environmentId: EnvironmentId,
  {
    autoRefreshRate,
    select,
    enabled,
    ...params
  }: UseModels & {
    autoRefreshRate?: number;
    select?: (data: ModelListViewModel[]) => T;
    enabled?: boolean;
  } = {}
) {
  return useQuery(
    queryKeys.filters(environmentId, params),
    () => getModels(environmentId, params),
    {
      ...withGlobalError('Unable to retrieve models'),
      refetchInterval: autoRefreshRate ?? false,
      select,
      enabled,
    }
  );
}

/**
 * Fetch models and return mock data for now
 * @param environmentId
 * @param param1
 * @returns ModelListViewModel[]
 */
export async function getModels(
  environmentId: EnvironmentId,
  { all = true, nodeName }: UseModels = {}
): Promise<ModelListViewModel[]> {
  // Mock data for now - in a real implementation this would call the Docker API
  // to fetch containers with specific labels indicating they are LLM models
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve([
        {
          // Core docker model ls properties
          ModelName: 'ai/smollm2',
          Parameters: '361.82 M',
          Quantization: 'IQ2_XXS/Q4_K_M',
          Architecture: 'llama',
          ModelId: '354bf30d0aa3',
          Created: Date.now() - 10368000000, // 4 months ago
          Size: '256.35 MiB',

          // UI properties
          Status: ModelStatus.Running,
          StatusText: 'Running',
          NodeName: nodeName || '',
          IsPortainer: false,

          // Legacy compatibility
          Id: '354bf30d0aa3',
          Names: ['ai/smollm2'],
        },
        {
          // Core docker model ls properties
          ModelName: 'hf.co/unsloth/qwen3-0.6b-gguf:q2_k_xl',
          Parameters: '596M',
          Quantization: 'Q2_K_XL',
          Architecture: 'qwen',
          ModelId: 'a1b2c3d4e5f6',
          Created: Date.now() - 2592000000, // 1 month ago
          Size: '512.8 MiB',

          // UI properties
          Status: ModelStatus.Stopped,
          StatusText: 'Stopped',
          NodeName: nodeName || '',
          IsPortainer: false,

          // Legacy compatibility
          Id: 'a1b2c3d4e5f6',
          Names: ['hf.co/unsloth/qwen3-0.6b-gguf:q2_k_xl'],
        },
        {
          // Core docker model ls properties
          ModelName: 'ollama/llama3.2:3b',
          Parameters: '3.21 B',
          Quantization: 'Q4_0',
          Architecture: 'llama',
          ModelId: '7f8e9d0c1b2a',
          Created: Date.now() - 604800000, // 1 week ago
          Size: '2.0 GiB',

          // UI properties
          Status: ModelStatus.Stopped,
          StatusText: 'Stopped',
          NodeName: nodeName || '',
          IsPortainer: false,

          // Legacy compatibility
          Id: '7f8e9d0c1b2a',
          Names: ['ollama/llama3.2:3b'],
        },
      ]);
    }, 500); // Simulate network delay
  });
}
